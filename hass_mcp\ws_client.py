"""WebSocket client for Home Assistant"""
import logging
import os
import ssl
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional, List, Callable

import anyio
import orjson as json
from aiohttp import (
    ClientSession,
    ClientWebSocketResponse,
    WSMsgType,
    ClientTimeout
)

from .decorators import require_connection
from .exceptions import (
    HassClientError,
    HassClientClosed,
    ConnectionError,
    AuthenticationError
)


_LOGGER = logging.getLogger(__name__)


class MessageType(Enum):
    """WebSocket message types"""
    AUTH_REQUIRED = "auth_required"
    AUTH = "auth"
    AUTH_OK = "auth_ok"
    AUTH_INVALID = "auth_invalid"
    RESULT = "result"
    EVENT = "event"
    PING = "ping"
    PONG = "pong"
    GET_STATES = "get_states"
    GET_CONFIG = "get_config"
    GET_SERVICES = "get_services"
    GET_AREA_REGISTRY = "config/area_registry/list"
    GET_DEVICE_REGISTRY = "config/device_registry/list"
    GET_ENTITY_REGISTRY = "config/entity_registry/list"
    GET_ENTITY_REGISTRY_ENTRY = "config/entity_registry/get"
    CALL_SERVICE = "call_service"
    SUBSCRIBE_EVENTS = "subscribe_events"
    UNSUBSCRIBE_EVENTS = "unsubscribe_events"
    SUBSCRIBE_TRIGGER = "subscribe_trigger"
    FIRE_EVENT = "fire_event"


class HassClient:
    """Home Assistant WebSocket client"""

    def __init__(self, 
        url: str = None, 
        token: str = None, 
        session : ClientSession = None,
    ):
        self._url = url
        self._token = token
        self._session = session
        self._tg = None
        self._websocket: ClientWebSocketResponse | None = None
        self._ssl_context = None
        self._message_id = 0
        self._pending_requests: Dict[int, anyio.Event] = {}
        self._pending_results: Dict[int, Any] = {}
        self._subscriptions: Dict[int, Callable] = {}
        self._connected = False

    @property
    def connected(self) -> bool:
        """check if connected"""
        return (self._connected and 
                self._websocket and 
                not self._websocket.closed)

    @property
    def ws_url(self) -> str:
        """format WebSocket URL"""
        base = self._url.replace("https://", "wss://").replace("http://", "ws://")
        return f"{base}/api/websocket"

    def get_session(self) -> ClientSession:
        """get aiohttp session"""
        if not self.connected:
            raise ConnectionError("not connected")
        return self._session

    @property
    def get_ssl_context(self) -> ssl.SSLContext:
        """get SSL context"""
        if not self.connected:
            raise ConnectionError("not connected")
        return self._ssl_context

    def _get_next_id(self) -> int:
        """get next message ID"""
        self._message_id += 1
        return self._message_id

    async def connect(self) -> None:
        """connect to Home Assistant"""
        if self.connected:
            _LOGGER.info("Already connected")
            return 
        if not self._url or not self._token or not self._tg:
            raise ValueError("missing HA_url or HA_token or task group")

        try:
            if self._session is None:
                self._session = ClientSession(
                    timeout=ClientTimeout(total=30)
                )

            cert_file_name = os.environ.get("HA_CERT")
            if cert_file_name:
                cert_path = Path.cwd() / cert_file_name
                if cert_path.exists():
                    self._ssl_context = ssl.create_default_context(cafile=str(cert_path))
                    _LOGGER.debug(f"Using self-signed certificate from {cert_path}")
                else:
                    _LOGGER.warning(f"Cert file specified in HASS_CERT_FILE not found: {cert_path}")
    
            self._websocket = await self._session.ws_connect(
                self.ws_url, heartbeat=55, ssl=self._ssl_context
            )

            await self._authenticate()

            async with anyio.create_task_group() as tg:
                self._tg = tg
                tg.start_soon(self.listen_messages)
                _LOGGER.info("WebSocket connected")

        except Exception as e:
            await self.disconnect()
            raise ConnectionError(f"connect error: {e}") from e

    async def _authenticate(self) -> None:
        """authenticate with Home Assistant"""
        data = await self._websocket.receive_json()
        
        if data.get("type") != MessageType.AUTH_REQUIRED.value:
            raise AuthenticationError("required auth message not received")

        auth_msg = {
            "type": MessageType.AUTH.value,
            "access_token": self._token
        }
        await self._websocket.send_json(auth_msg)

        data = await self._websocket.receive_json()
        
        if data.get("type") == MessageType.AUTH_OK.value:
            self._connected = True
            _LOGGER.info("authenticated")
        else:
            raise AuthenticationError(f"auth failed: {data.get('message')}")

    async def disconnect(self):
        """disconnect from Home Assistant"""
        self._connected = False

        if self._tg:
            self._tg.cancel_scope.cancel()

        if self._pending_requests:
            for event in self._pending_requests.values():
                event.set()
        if self._subscriptions:
            for sub_id in self._subscriptions.keys():
                await self.unsubscribe_events(sub_id)

        self._pending_requests.clear()
        self._pending_results.clear()
        self._subscriptions.clear()

        if self._websocket and not self._websocket.closed:
            await self._websocket.close()
        
        if self._session:
            await self._session.close()
            self._session = None

        _LOGGER.info("WebSocket disconnected")

    async def listen_messages(self):
        """listen for websocket messages"""
        if not self.connected:
            raise ConnectionError("not connected")

        try:
            async for msg in self._websocket:
                if msg.type == WSMsgType.TEXT:
                    data = json.loads(msg.data)
                    await self._handle_message(data)
                elif msg.type == WSMsgType.ERROR:
                    err = self._websocket.exception()
                    raise HassClientError(f"WebSocket error: {err}")
                elif msg.type in (WSMsgType.CLOSE, WSMsgType.CLOSED, WSMsgType.CLOSING):
                    raise HassClientClosed("WebSocket closed")
        finally:
            self._connected = False

    async def _handle_message(self, data: Dict[str, Any]):
        """handle incoming message"""
        msg_type = data.get("type")
        msg_id = data.get("id")

        if msg_type == MessageType.RESULT.value and msg_id in self._pending_requests:
            if data.get("success"):
                self._pending_results[msg_id] = data.get("result")
            else:
                error = data.get("error", {})
                self._pending_results[msg_id] = Exception(f"{error.get('code')}: {error.get('message')}")
            
            self._pending_requests[msg_id].set()

        elif msg_type == MessageType.EVENT.value and msg_id in self._subscriptions:
            # callback for event
            handler = self._subscriptions[msg_id]
            self._tg.start_soon(handler, data.get("event"))
    
    @require_connection
    async def send_command(self, command_type: str, timeout: float = 10.0, **kwargs) -> Any:
        """send command to Home Assistant"""
        msg_id = self._get_next_id()
        message = {
            "id": msg_id,
            "type": command_type,
            **kwargs
        }

        event = anyio.Event()
        self._pending_requests[msg_id] = event

        try:
            await self._websocket.send_json(message)
            
            with anyio.fail_after(timeout):
                await event.wait()
                result = self._pending_results.get(msg_id) 

                if isinstance(result, Exception):
                    raise result

                if command_type == MessageType.SUBSCRIBE_EVENTS.value:
                    return msg_id, result

                return result 
        except TimeoutError:
            raise TimeoutError(f"wait for {command_type} response timeout")
        except Exception as e:
            raise HassClientError(f"send command error: {e}") from e
        finally:
            self._pending_requests.pop(msg_id, None)
            self._pending_results.pop(msg_id, None)

    @require_connection
    async def get_entity_state(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """get entity state"""
        states = await self.get_states()
        state_dict = {
            state["entity_id"]: state 
            for state in states
            if state["entity_id"] == entity_id
        }

        return state_dict if state_dict else None
    
    @require_connection
    async def get_states(self) -> Optional[List[Dict[str, Any]]]:
        """get all entity states"""
        return await self.send_command(MessageType.GET_STATES.value)

    @require_connection
    async def get_config(self) -> Optional[Dict[str, Any]]:
        """get Home Assistant config"""
        return await self.send_command(MessageType.GET_CONFIG.value)

    @require_connection
    async def get_services(self) -> Optional[Dict[str, Dict[str, Any]]]:
        """get all services"""
        return await self.send_command(MessageType.GET_SERVICES.value)
        
    @require_connection
    async def get_area_registry(self) -> Optional[List[Dict[str, Any]]]:
        """get area registry"""
        return await self.send_command(MessageType.GET_AREA_REGISTRY.value)
        
    @require_connection
    async def get_device_registry(self) -> Optional[List[Dict[str, Any]]]:
        """get device registry"""
        return await self.send_command(MessageType.GET_DEVICE_REGISTRY.value)

    @require_connection
    async def get_entity_registry(self) -> Optional[List[Dict[str, Any]]]:
        """get entity registry"""
        return await self.send_command(MessageType.GET_ENTITY_REGISTRY.value)

    @require_connection
    async def get_entity_registry_entry(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """get single entry from entity registry"""
        return await self.send_command(MessageType.GET_ENTITY_REGISTRY_ENTRY.value, entity_id=entity_id)

    @require_connection
    async def call_service(
        self, 
        domain: str, 
        service: str, 
        service_data: Dict[str, Any] = None, 
        target: Dict[str, Any] = None
    ) -> Optional[Dict[str, Any]]:
        """call service"""
        command = {"domain": domain, "service": service, "return_response": True}
        if service_data:
            command["service_data"] = service_data
        if target:
            command["target"] = target
        
        return await self.send_command(MessageType.CALL_SERVICE.value, **command)

    @require_connection
    async def fire_event(self, event_type: str, event_data: Dict[str, Any] = None) -> bool:
        """fire event"""
        command = {"event_type": event_type}
        if event_data:
            command["event_data"] = event_data

        await self.send_command(MessageType.FIRE_EVENT.value, **command)
        
        return True

    @require_connection
    async def subscribe_events(self, event_type: str = None, handler: Callable = None) -> int:
        """subscribe to events"""
        if not handler:
            _LOGGER.error("callback is None")
            return

        command = {}
        if event_type:
            command["event_type"] = event_type

        msg_id, _= await self.send_command(MessageType.SUBSCRIBE_EVENTS.value, **command)
        self._subscriptions[msg_id] = handler

        return msg_id

    @require_connection
    async def unsubscribe_events(self, subscription_id: int) -> bool:
        """unsubscribe from events"""
        await self.send_command(
            MessageType.UNSUBSCRIBE_EVENTS.value,
            subscription=subscription_id
        )

        self._subscriptions.pop(subscription_id, None)

        return True
    
    @require_connection
    async def subscribe_trigger(
        self, 
        trigger_config: Dict[str, Any], 
        handler: Callable = None
    ) -> int:
        """subscribe to trigger"""
        if not handler:
            _LOGGER.error("callback is None")
            return

        msg_id, _ = await self.send_command(
            MessageType.SUBSCRIBE_TRIGGER.value,
            trigger=trigger_config
        )

        self._subscriptions[msg_id] = handler

        return msg_id
    
    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.disconnect()