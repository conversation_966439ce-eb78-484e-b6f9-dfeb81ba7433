# 異步Redis集成改進

本文檔說明了將Redis集成改為完全異步操作並使用原子操作避免競爭條件的重要改進。

## 主要改進

### 1. 完全異步操作
- 使用 `redis.asyncio` 模組替代同步版本
- 所有Redis操作都使用 `await` 關鍵字
- 異步連接池管理，提高並發性能
- 支援異步上下文管理器 (`async with`)

### 2. 原子操作保證
- 使用Lua腳本確保複雜操作的原子性
- 事務管道 (`pipeline(transaction=True)`) 用於批量操作
- WATCH命令監控關鍵鍵的變化
- 避免競爭條件和數據不一致

### 3. 性能優化
- 批量操作使用管道減少網絡往返
- 連接池復用減少連接開銷
- 智能重試機制處理臨時故障
- 健康檢查和監控功能

## 技術實現

### 異步連接管理
```python
async with RedisManager() as redis_manager:
    # 自動連接和斷開
    await redis_manager.cache_entity(entity_data)
```

### 原子操作示例
```python
# 使用Lua腳本的原子實體緩存
await redis_manager.cache_entity(entity_data)

# 批量原子操作
await redis_manager.cache_entities_batch(entities)
```

### 事務管道
```python
async with self._redis.pipeline(transaction=True) as pipe:
    # 所有操作要麼全部成功，要麼全部失敗
    pipe.hset(entity_key, mapping=cache_data)
    pipe.sadd(domain_key, entity_id)
    results = await pipe.execute()
```

## Lua腳本

### 實體緩存腳本
```lua
-- 原子性地緩存實體並更新域名集合和元數據
local entity_key = KEYS[1]
local domain_key = KEYS[2]
local metadata_key = KEYS[3]

redis.call('HSET', entity_key, unpack(entity_data))
redis.call('SADD', domain_key, entity_id)

local count = redis.call('SCARD', domain_key)
redis.call('HSET', metadata_key, domain .. '_count', count)
```

### 實體移除腳本
```lua
-- 原子性地移除實體並更新相關數據結構
redis.call('DEL', entity_key)
redis.call('SREM', domain_key, entity_id)

local count = redis.call('SCARD', domain_key)
if count > 0 then
    redis.call('HSET', metadata_key, domain .. '_count', count)
else
    redis.call('HDEL', metadata_key, domain .. '_count')
end
```

## 競爭條件預防

### 1. 實體更新競爭
**問題**: 多個進程同時更新同一實體
**解決**: 使用Lua腳本確保更新操作原子性

### 2. 域名計數競爭
**問題**: 實體添加/刪除時域名計數不準確
**解決**: 在同一事務中更新實體和計數

### 3. 批量操作競爭
**問題**: 批量操作中部分成功部分失敗
**解決**: 使用事務管道確保全部成功或全部失敗

## 性能提升

### 批量操作性能
- **個別操作**: 100個實體需要100次網絡往返
- **批量操作**: 100個實體只需要1次網絡往返
- **性能提升**: 通常提升10-50倍

### 連接池優勢
- 減少連接建立/關閉開銷
- 支援並發操作
- 自動故障恢復
- 連接健康檢查

### 異步優勢
- 非阻塞I/O操作
- 更好的資源利用率
- 支援高並發場景
- 減少線程開銷

## 錯誤處理

### 連接錯誤
```python
try:
    await redis_manager.connect()
except HassClientError as e:
    _LOGGER.error(f"Redis connection failed: {e}")
    # 自動重試或降級處理
```

### 事務錯誤
```python
try:
    async with self._redis.pipeline(transaction=True) as pipe:
        # 事務操作
        await pipe.execute()
except WatchError:
    # 重試事務
    await self._retry_transaction()
```

### Lua腳本錯誤
```python
try:
    return await self._redis.evalsha(script_sha, keys, args)
except Exception as e:
    if "NOSCRIPT" in str(e):
        # 重新註冊腳本並重試
        await self._register_lua_scripts()
        return await self._redis.evalsha(script_sha, keys, args)
```

## 監控和診斷

### 健康檢查
```python
health = await redis_manager.health_check()
# 返回: ping時間、索引狀態、連接池狀態
```

### 性能統計
```python
stats = await redis_manager.get_cache_stats()
# 返回: 實體總數、域名分佈、內存使用、連接信息
```

### 連接池監控
```python
pool_info = {
    "created_connections": pool.created_connections,
    "available_connections": len(pool._available_connections)
}
```

## 最佳實踐

### 1. 使用上下文管理器
```python
async with RedisManager() as redis_manager:
    # 自動處理連接和清理
    pass
```

### 2. 批量操作優先
```python
# 好的做法
await redis_manager.cache_entities_batch(entities)

# 避免的做法
for entity in entities:
    await redis_manager.cache_entity(entity)
```

### 3. 錯誤處理
```python
try:
    await redis_operation()
except HassClientError:
    # 處理Redis特定錯誤
except Exception:
    # 處理其他錯誤
```

### 4. 資源清理
```python
try:
    # Redis操作
    pass
finally:
    await redis_manager.disconnect()
```

## 測試驗證

### 原子性測試
- 並發更新測試
- 事務回滾測試
- 競爭條件模擬

### 性能測試
- 批量vs個別操作對比
- 並發操作壓力測試
- 內存使用監控

### 可靠性測試
- 連接中斷恢復
- 腳本重新註冊
- 錯誤處理驗證

## 遷移指南

### 從同步到異步
1. 更新導入: `import redis.asyncio as redis`
2. 添加await: `await redis_operation()`
3. 使用上下文管理器: `async with RedisManager()`
4. 更新錯誤處理

### 配置更新
```python
# 舊配置
redis_manager = RedisManager()
redis_manager.connect()

# 新配置
async with RedisManager() as redis_manager:
    # 操作
    pass
```

## 未來改進

1. **分佈式鎖**: 實現Redis分佈式鎖機制
2. **流水線優化**: 更智能的批量操作分組
3. **緩存策略**: 基於訪問模式的智能緩存
4. **監控儀表板**: 實時性能監控界面
5. **自動調優**: 基於負載的參數自動調整

這些改進大大提升了Redis集成的性能、可靠性和可維護性，為Home Assistant MCP Server提供了更強大的緩存和搜索能力。
