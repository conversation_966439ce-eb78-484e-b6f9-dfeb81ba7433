"""Async Redis manager for Home Assistant entity caching and search"""
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime, timezone

import anyio
import orjson as json
import redis.asyncio as redis
from redis.commands.search.field import <PERSON><PERSON>ield, TagField, Numeric<PERSON>ield
from redis.commands.search.indexDefinition import IndexDefinition, IndexType
from redis.commands.search.query import Query
from redis.exceptions import WatchError, ResponseError

from .exceptions import HassClientError

_LOGGER = logging.getLogger(__name__)


class RedisManager:
    """Optimized Async Redis manager for entity caching and search indexing"""

    def __init__(self, redis_url: str = "redis://localhost:6379", redis_db: int = 0):
        """Initialize Redis manager

        Args:
            redis_url: Redis connection URL
            redis_db: Redis database number
        """
        self.redis_url = redis_url
        self.redis_db = redis_db
        self._redis: Optional[redis.Redis] = None
        self._connection_pool: Optional[redis.ConnectionPool] = None
        
        # Configuration constants
        self._index_name = "hass_entities"
        self._entity_prefix = "entity:"
        self._domain_prefix = "domain:"
        self._metadata_key = "hass:metadata"
        
        # Lua scripts registry
        self._lua_scripts: Dict[str, Dict[str, str]] = {}
        self._scripts_registered = False
        
        # Performance settings
        self._max_batch_size = 1000
        self._pipeline_buffer_size = 100

    async def connect(self):
        """Connect to Redis with optimized settings"""
        if self._redis is not None:
            return  # Already connected

        try:
            # Optimized connection pool
            self._connection_pool = redis.ConnectionPool.from_url(
                self.redis_url,
                db=self.redis_db,
                decode_responses=False,  # 使用 bytes 避免編碼開銷
                socket_connect_timeout=10,
                socket_timeout=10,
                retry_on_timeout=True,
                retry_on_error=[redis.ConnectionError, redis.TimeoutError],
                health_check_interval=30,
                max_connections=20  # 限制連接數
            )

            self._redis = redis.Redis(connection_pool=self._connection_pool)

            # Test connection
            await self._redis.ping()
            _LOGGER.info("Connected to Redis successfully")

            # Lazy initialization
            await self._ensure_scripts_registered()
            await self._ensure_search_index()

        except Exception as e:
            _LOGGER.error(f"Failed to connect to Redis: {e}")
            await self.disconnect()
            raise HassClientError(f"Redis connection failed: {e}")

    async def disconnect(self):
        """Disconnect from Redis with proper cleanup"""
        errors = []
        
        if self._redis:
            try:
                await self._redis.aclose()
            except Exception as e:
                errors.append(f"Redis connection: {e}")
            finally:
                self._redis = None

        if self._connection_pool:
            try:
                await self._connection_pool.aclose()
            except Exception as e:
                errors.append(f"Connection pool: {e}")
            finally:
                self._connection_pool = None

        if errors:
            _LOGGER.warning(f"Cleanup errors: {'; '.join(errors)}")
        else:
            _LOGGER.info("Disconnected from Redis")

    async def _ensure_scripts_registered(self):
        """Lazy registration of Lua scripts"""
        if self._scripts_registered:
            return
            
        try:
            cache_script = """
                local entity_key, domain_key, metadata_key = KEYS[1], KEYS[2], KEYS[3]
                local entity_id, domain = ARGV[1], ARGV[2]
                
                local fields = {}
                for i = 3, #ARGV, 2 do
                    fields[#fields + 1] = ARGV[i]
                    fields[#fields + 1] = ARGV[i + 1]
                end
                
                if #fields > 0 then
                    redis.call('HSET', entity_key, unpack(fields))
                    redis.call('SADD', domain_key, entity_id)
                    local count = redis.call('SCARD', domain_key)
                    redis.call('HSET', metadata_key, domain .. '_count', count)
                end
                
                return 'OK'
            """
            
            remove_script = """
                local entity_key, domain_key, metadata_key = KEYS[1], KEYS[2], KEYS[3]
                local entity_id, domain = ARGV[1], ARGV[2]
                
                redis.call('DEL', entity_key)
                redis.call('SREM', domain_key, entity_id)
                
                local count = redis.call('SCARD', domain_key)
                if count > 0 then
                    redis.call('HSET', metadata_key, domain .. '_count', count)
                else
                    redis.call('HDEL', metadata_key, domain .. '_count')
                end
                
                return count
            """

            scripts = {
                'cache_entity': cache_script,
                'remove_entity': remove_script
            }
            
            for name, code in scripts.items():
                sha = await self._redis.script_load(code)
                self._lua_scripts[name] = {'code': code, 'sha': sha}
            
            self._scripts_registered = True
            _LOGGER.info(f"Registered {len(scripts)} Lua scripts")
            
        except Exception as e:
            _LOGGER.error(f"Failed to register Lua scripts: {e}")
            raise

    async def _ensure_search_index(self):
        """Create search index if not exists (idempotent)"""
        try:
            await self._redis.ft(self._index_name).info()
            return 
        except ResponseError:
            pass 

        try:
            schema = (
                TextField("entity_id", weight=5.0),
                TextField("friendly_name", weight=3.0),
                TextField("state"),
                TagField("domain"),
                TagField("device_class"),
                TextField("area_name"),
                NumericField("last_updated_ts"),
            )

            definition = IndexDefinition(
                prefix=[self._entity_prefix.encode()],  # Use bytes
                index_type=IndexType.HASH
            )

            await self._redis.ft(self._index_name).create_index(
                fields=schema,
                definition=definition
            )

            _LOGGER.info(f"Created search index '{self._index_name}'")

        except Exception as e:
            _LOGGER.error(f"Failed to create search index: {e}")

    def _parse_timestamp(self, timestamp_str: str) -> float:
        """Optimized timestamp parsing"""
        if not timestamp_str:
            return 0.0
        try:
            # 處理常見的 ISO 格式
            if timestamp_str.endswith('Z'):
                timestamp_str = timestamp_str[:-1] + '+00:00'
            return datetime.fromisoformat(timestamp_str).timestamp()
        except (ValueError, TypeError):
            return 0.0

    def _prepare_cache_data(self, entity_data: Dict[str, Any], cached_at: str) -> Dict[str, Any]:
        """Prepare entity data for Redis (optimized)"""
        entity_id = entity_data.get("entity_id", "")
        attributes = entity_data.get("attributes", {})
        
        # 提取關鍵字段，避免深度訪問
        return {
            "entity_id": entity_id,
            "state": str(entity_data.get("state", "")),
            "domain": entity_id.split(".", 1)[0] if entity_id else "",
            "friendly_name": str(attributes.get("friendly_name", entity_id)),
            "device_class": str(attributes.get("device_class", "")),
            "area_name": str(attributes.get("area_name", "")),
            "last_updated": str(entity_data.get("last_updated", "")),
            "last_updated_ts": str(self._parse_timestamp(entity_data.get("last_updated", ""))),
            "attributes": attributes,  # 在 pipeline 中序列化
            "cached_at": cached_at
        }

    async def cache_entities_batch(self, entities: List[Dict[str, Any]], batch_size: int = 500) -> int:
        """Optimized batch caching with better error handling"""
        if not self._redis or not entities:
            return 0

        # 限制批次大小防止記憶體問題
        batch_size = min(batch_size, self._max_batch_size)
        total_entities = len(entities)
        cached_count = 0

        _LOGGER.info(f"Caching {total_entities} entities in batches of {batch_size}")

        try:
            for i in range(0, total_entities, batch_size):
                batch = entities[i:i + batch_size]
                batch_cached = await self._cache_batch_optimized(batch)
                cached_count += batch_cached
                
                if i + batch_size < total_entities:
                    await anyio.sleep(0.001)

            _LOGGER.info(f"Successfully cached {cached_count}/{total_entities} entities")
            return cached_count

        except Exception as e:
            _LOGGER.error(f"Batch caching failed: {e}")
            raise HassClientError(f"Failed to cache entities: {e}")

    async def _cache_batch_optimized(self, entities: List[Dict[str, Any]]) -> int:
        """Optimized atomic batch caching using pipeline"""
        if not entities:
            return 0

        cached_at = datetime.now(timezone.utc).isoformat()
        domains = set()
        cached_count = 0

        try:
            async with self._redis.pipeline(transaction=True) as pipe:
                for entity_data in entities:
                    entity_id = entity_data.get("entity_id")
                    if not entity_id:
                        continue

                    try:
                        cache_data = self._prepare_cache_data(entity_data, cached_at)
                        domain = cache_data["domain"]
                        domains.add(domain)

                        entity_key = f"{self._entity_prefix}{entity_id}".encode()
                        domain_key = f"{self._domain_prefix}{domain}".encode()

                        cache_data["attributes"] = json.dumps(cache_data["attributes"])
                        
                        pipe.hset(entity_key, mapping=cache_data)
                        pipe.sadd(domain_key, entity_id.encode())
                        cached_count += 1

                    except Exception as e:
                        _LOGGER.warning(f"Skipping invalid entity {entity_id}: {e}")
                        continue

                # 批次更新域計數
                for domain in domains:
                    domain_key = f"{self._domain_prefix}{domain}".encode()
                    pipe.scard(domain_key)

                # 執行所有操作
                results = await pipe.execute()
                
                # 更新元數據
                if domains:
                    await self._update_domain_counts(domains, results[-len(domains):])

        except WatchError:
            _LOGGER.warning("Transaction conflict, retrying batch")
            return await self._cache_batch_optimized(entities)
        except Exception as e:
            _LOGGER.error(f"Batch cache operation failed: {e}")
            raise

        return cached_count

    async def _update_domain_counts(self, domains: Set[str], counts: List[int]):
        """Update domain counts in metadata"""
        if not domains or not counts:
            return

        try:
            updates = {f"{domain}_count": count for domain, count in zip(domains, counts) if count > 0}
            if updates:
                await self._redis.hset(self._metadata_key.encode(), mapping=updates)
        except Exception as e:
            _LOGGER.warning(f"Failed to update domain counts: {e}")

    async def get_cached_entities_batch(self, entity_ids: List[str]) -> Dict[str, Optional[Dict[str, Any]]]:
        """Optimized batch retrieval"""
        if not self._redis or not entity_ids:
            return {}

        try:
            # 使用 pipeline 批次取得
            async with self._redis.pipeline() as pipe:
                for entity_id in entity_ids:
                    pipe.hgetall(f"{self._entity_prefix}{entity_id}".encode())
                
                results = await pipe.execute()

            # 處理結果
            entities = {}
            for entity_id, data in zip(entity_ids, results):
                if data:
                    # 解碼和反序列化
                    decoded_data = {k.decode(): v.decode() if isinstance(v, bytes) else v for k, v in data.items()}
                    
                    # 恢復屬性
                    if decoded_data.get("attributes"):
                        try:
                            decoded_data["attributes"] = json.loads(decoded_data["attributes"])
                        except json.JSONDecodeError:
                            decoded_data["attributes"] = {}
                    
                    # 轉換數值類型
                    if decoded_data.get("last_updated_ts"):
                        try:
                            decoded_data["last_updated_ts"] = float(decoded_data["last_updated_ts"])
                        except (ValueError, TypeError):
                            decoded_data["last_updated_ts"] = 0.0
                    
                    entities[entity_id] = decoded_data
                else:
                    entities[entity_id] = None

            return entities

        except Exception as e:
            _LOGGER.error(f"Batch retrieval failed: {e}")
            return {entity_id: None for entity_id in entity_ids}

    async def search_entities(self, query: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Optimized entity search with better error handling"""
        if not self._redis or not query.strip():
            return []

        try:
            # 構建搜索查詢，增加超時
            search_query = Query(query).paging(offset, limit).timeout(3000)
            result = await self._redis.ft(self._index_name).search(search_query)

            entities = []
            for doc in result.docs:
                try:
                    entity_data = dict(doc.__dict__)
                    entity_data.pop("id", None)
                    entity_data.pop("payload", None)

                    # 處理屬性反序列化
                    if entity_data.get("attributes"):
                        try:
                            entity_data["attributes"] = json.loads(entity_data["attributes"])
                        except json.JSONDecodeError:
                            entity_data["attributes"] = {}

                    entities.append(entity_data)
                except Exception as e:
                    _LOGGER.warning(f"Skipping malformed search result: {e}")
                    continue

            return entities

        except Exception as e:
            _LOGGER.error(f"Search failed for query '{query}': {e}")
            return []

    async def remove_entities_batch(self, entity_ids: List[str]) -> int:
        """Optimized batch removal"""
        if not self._redis or not entity_ids:
            return 0

        try:
            # 按域分組提升效率
            domain_groups = {}
            for entity_id in entity_ids:
                domain = entity_id.split(".", 1)[0]
                domain_groups.setdefault(domain, []).append(entity_id)

            removed_count = 0
            async with self._redis.pipeline(transaction=True) as pipe:
                for domain, entities in domain_groups.items():
                    domain_key = f"{self._domain_prefix}{domain}".encode()
                    
                    for entity_id in entities:
                        entity_key = f"{self._entity_prefix}{entity_id}".encode()
                        pipe.delete(entity_key)
                        pipe.srem(domain_key, entity_id.encode())
                        removed_count += 1
                    
                    pipe.scard(domain_key)

                results = await pipe.execute()
                
                # 更新域計數
                count_results = [results[i] for i in range(len(results)) if (i + 1) % (2 * len(domain_groups) + 1) == 0]
                await self._update_domain_counts(set(domain_groups.keys()), count_results)

            _LOGGER.info(f"Removed {removed_count} entities")
            return removed_count

        except Exception as e:
            _LOGGER.error(f"Batch removal failed: {e}")
            return 0

    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics"""
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            async with self._redis.pipeline() as pipe:
                pipe.eval("return #redis.call('keys', ARGV[1])", 0, f"{self._entity_prefix}*")
                pipe.hgetall(self._metadata_key.encode())
                pipe.info("memory")
                pipe.info("clients")
                
                results = await pipe.execute()

            total_entities, metadata, memory_info, clients_info = results

            # 解碼元數據
            if isinstance(metadata, dict):
                metadata = {k.decode() if isinstance(k, bytes) else k: 
                           v.decode() if isinstance(v, bytes) else v 
                           for k, v in metadata.items()}

            return {
                "total_entities": total_entities,
                "domain_counts": metadata,
                "redis_info": {
                    "used_memory": memory_info.get("used_memory_human", "unknown"),
                    "connected_clients": clients_info.get("connected_clients", 0)
                }
            }
            
        except Exception as e:
            _LOGGER.error(f"Failed to get cache stats: {e}")
            return {}

    async def cache_entity(self, entity_data: Dict[str, Any]) -> bool:
        """Cache single entity (convenience method)"""
        try:
            result = await self.cache_entities_batch([entity_data], batch_size=1)
            return result > 0
        except Exception as e:
            _LOGGER.error(f"Failed to cache entity {entity_data.get('entity_id', 'unknown')}: {e}")
            return False

    async def get_cached_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """Get single cached entity (convenience method)"""
        results = await self.get_cached_entities_batch([entity_id])
        return results.get(entity_id)

    async def remove_entity(self, entity_id: str) -> bool:
        """Remove single entity (convenience method)"""
        try:
            result = await self.remove_entities_batch([entity_id])
            return result > 0
        except Exception as e:
            _LOGGER.error(f"Failed to remove entity {entity_id}: {e}")
            return False
