"""Async Redis manager for Home Assistant entity caching and search"""
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

import anyio
import orjson as json
import redis.asyncio as redis
from redis.commands.search.field import <PERSON><PERSON>ield, TagField, Numeric<PERSON>ield
from redis.commands.search.indexDefinition import IndexDefinition, IndexType
from redis.commands.search.query import Query
from redis.exceptions import WatchError

from .exceptions import HassClientError

_LOGGER = logging.getLogger(__name__)


class RedisManager:
    """Async Redis manager for entity caching and search indexing"""

    def __init__(self, redis_url: str = "redis://localhost:6379", redis_db: int = 0):
        """Initialize Redis manager

        Args:
            redis_url: Redis connection URL
            redis_db: Redis database number
        """
        self.redis_url = redis_url
        self.redis_db = redis_db
        self._redis: Optional[redis.Redis] = None
        self._connection_pool: Optional[redis.ConnectionPool] = None
        self._index_name = "hass_entities"
        self._entity_prefix = "entity:"
        self._domain_prefix = "domain:"
        self._metadata_key = "hass:metadata"
        # Lua scripts for atomic operations
        self._lua_scripts = {}
        self._init_lua_scripts()

    def _init_lua_scripts(self):
        """Initialize Lua scripts for atomic operations"""

        # Script for atomic entity caching with domain set update
        self._lua_scripts['cache_entity'] = """
            local entity_key = KEYS[1]
            local domain_key = KEYS[2]
            local metadata_key = KEYS[3]
            local entity_id = ARGV[1]
            local domain = ARGV[2]

            -- Set entity data (ARGV[3] onwards are field-value pairs)
            local entity_data = {}
            for i = 3, #ARGV, 2 do
                entity_data[ARGV[i]] = ARGV[i + 1]
            end

            -- Atomic operations
            redis.call('HSET', entity_key, unpack(entity_data))
            redis.call('SADD', domain_key, entity_id)

            -- Update domain count in metadata
            local count = redis.call('SCARD', domain_key)
            redis.call('HSET', metadata_key, domain .. '_count', count)

            return 'OK'
        """

        # Script for atomic entity removal
        self._lua_scripts['remove_entity'] = """
            local entity_key = KEYS[1]
            local domain_key = KEYS[2]
            local metadata_key = KEYS[3]
            local entity_id = ARGV[1]
            local domain = ARGV[2]

            -- Remove entity and update domain set
            redis.call('DEL', entity_key)
            redis.call('SREM', domain_key, entity_id)

            -- Update domain count in metadata
            local count = redis.call('SCARD', domain_key)
            if count > 0 then
                redis.call('HSET', metadata_key, domain .. '_count', count)
            else
                redis.call('HDEL', metadata_key, domain .. '_count')
            end

            return 'OK'
        """

        # Script for atomic batch entity caching
        self._lua_scripts['cache_entities_batch'] = """
            local metadata_key = KEYS[1]
            local domains = {}

            -- Process each entity (groups of 3: entity_key, domain_key, entity_id, domain, field-value pairs)
            local i = 2
            while i <= #KEYS do
                local entity_key = KEYS[i]
                local domain_key = KEYS[i + 1]
                local entity_id = ARGV[i - 1]
                local domain = ARGV[i]

                -- Find entity data start index
                local data_start = i + 1
                local entity_data = {}
                local j = data_start
                while j < #ARGV and ARGV[j] ~= '__NEXT_ENTITY__' do
                    entity_data[#entity_data + 1] = ARGV[j]
                    j = j + 1
                end

                if #entity_data > 0 then
                    redis.call('HSET', entity_key, unpack(entity_data))
                    redis.call('SADD', domain_key, entity_id)
                    domains[domain] = true
                end

                i = i + 2
            end

            -- Update metadata for all affected domains
            for domain, _ in pairs(domains) do
                local domain_key = 'domain:' .. domain
                local count = redis.call('SCARD', domain_key)
                redis.call('HSET', metadata_key, domain .. '_count', count)
            end

            return 'OK'
        """

    async def connect(self):
        """Connect to Redis with async connection pool"""
        if self._redis is not None:
            return  # Already connected

        try:
            # Create connection pool
            self._connection_pool = redis.ConnectionPool.from_url(
                self.redis_url,
                db=self.redis_db,
                decode_responses=True,
                socket_connect_timeout=5,
                socket_timeout=5,
                retry_on_timeout=True,
                health_check_interval=30
            )

            # Create Redis client from pool
            self._redis = redis.Redis(connection_pool=self._connection_pool)

            # Test connection
            await self._redis.ping()
            _LOGGER.info("Connected to Redis successfully")

            # Register Lua scripts
            await self._register_lua_scripts()

            # Create search index if not exists
            await self._create_search_index()

        except Exception as e:
            _LOGGER.error(f"Failed to connect to Redis: {e}")
            await self.disconnect()
            raise HassClientError(f"Redis connection failed: {e}")

    async def disconnect(self):
        """Disconnect from Redis and cleanup resources"""
        if self._redis:
            try:
                await self._redis.aclose()
            except Exception as e:
                _LOGGER.warning(f"Error closing Redis connection: {e}")
            finally:
                self._redis = None

        if self._connection_pool:
            try:
                await self._connection_pool.aclose()
            except Exception as e:
                _LOGGER.warning(f"Error closing Redis connection pool: {e}")
            finally:
                self._connection_pool = None

        _LOGGER.info("Disconnected from Redis")

    async def _register_lua_scripts(self):
        """Register Lua scripts with Redis"""
        try:
            for script_name, script_code in self._lua_scripts.items():
                # Register script and store the SHA
                sha = await self._redis.script_load(script_code)
                self._lua_scripts[script_name] = {
                    'code': script_code,
                    'sha': sha
                }
            _LOGGER.info(f"Registered {len(self._lua_scripts)} Lua scripts")
        except Exception as e:
            _LOGGER.error(f"Failed to register Lua scripts: {e}")
            raise
    
    async def _create_search_index(self):
        """Create RediSearch index for entities"""
        try:
            # Check if index already exists
            try:
                await self._redis.ft(self._index_name).info()
                _LOGGER.info(f"Search index '{self._index_name}' already exists")
                return
            except:
                # Index doesn't exist, create it
                pass

            # Define schema for entity search
            schema = (
                TextField("entity_id", weight=5.0, as_name="entity_id"),
                TextField("friendly_name", weight=3.0, as_name="friendly_name"),
                TextField("state", as_name="state"),
                TagField("domain", as_name="domain"),
                TagField("device_class", as_name="device_class"),
                TextField("area_name", as_name="area_name"),
                TextField("attributes", as_name="attributes"),
                NumericField("last_updated_ts", as_name="last_updated_ts"),
            )

            # Create index definition
            definition = IndexDefinition(
                prefix=[self._entity_prefix],
                index_type=IndexType.HASH
            )

            # Create the index
            await self._redis.ft(self._index_name).create_index(
                fields=schema,
                definition=definition
            )

            _LOGGER.info(f"Created search index '{self._index_name}' successfully")

        except Exception as e:
            _LOGGER.error(f"Failed to create search index: {e}")
            raise HassClientError(f"Search index creation failed: {e}")
    
    async def cache_entity(self, entity_data: Dict[str, Any]):
        """Cache entity data in Redis using atomic operations

        Args:
            entity_data: Entity data from Home Assistant
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        try:
            entity_id = entity_data.get("entity_id")
            if not entity_id:
                _LOGGER.warning("Entity data missing entity_id")
                return

            # Extract domain from entity_id
            domain = entity_id.split(".")[0]

            # Prepare data for Redis
            cache_data = {
                "entity_id": entity_id,
                "state": entity_data.get("state", ""),
                "domain": domain,
                "friendly_name": entity_data.get("attributes", {}).get("friendly_name", entity_id),
                "device_class": entity_data.get("attributes", {}).get("device_class", ""),
                "area_name": entity_data.get("attributes", {}).get("area_name", ""),
                "last_updated": entity_data.get("last_updated", ""),
                "last_updated_ts": self._parse_timestamp(entity_data.get("last_updated", "")),
                "attributes": entity_data.get("attributes", {}),
                "cached_at": datetime.now(timezone.utc).isoformat()
            }

            # Use atomic Lua script for caching
            entity_key = f"{self._entity_prefix}{entity_id}"
            domain_key = f"{self._domain_prefix}{domain}"

            # Flatten cache_data for Lua script
            script_args = [entity_id, domain]
            for key, value in cache_data.items():
                script_args.extend([key, str(value)])

            # Execute atomic cache operation
            await self._execute_lua_script(
                'cache_entity',
                keys=[entity_key, domain_key, self._metadata_key],
                args=script_args
            )

            _LOGGER.debug(f"Cached entity: {entity_id}")

        except Exception as e:
            _LOGGER.error(f"Failed to cache entity {entity_data.get('entity_id', 'unknown')}: {e}")

    async def _execute_lua_script(self, script_name: str, keys: List[str], args: List[str]) -> Any:
        """Execute a Lua script with retry on script not found"""
        try:
            script_info = self._lua_scripts.get(script_name)
            if not script_info:
                raise HassClientError(f"Lua script '{script_name}' not found")

            # Try to execute using SHA first (more efficient)
            try:
                return await self._redis.evalsha(script_info['sha'], len(keys), *keys, *args)
            except Exception as e:
                if "NOSCRIPT" in str(e):
                    # Script not in cache, re-register and retry
                    _LOGGER.warning(f"Re-registering Lua script '{script_name}'")
                    sha = await self._redis.script_load(script_info['code'])
                    self._lua_scripts[script_name]['sha'] = sha
                    return await self._redis.evalsha(sha, len(keys), *keys, *args)
                else:
                    raise

        except Exception as e:
            _LOGGER.error(f"Failed to execute Lua script '{script_name}': {e}")
            raise
    
    async def cache_entities_batch(self, entities: List[Dict[str, Any]], batch_size: int = 100):
        """Cache multiple entities in atomic batch operations

        Args:
            entities: List of entity data from Home Assistant
            batch_size: Number of entities to process in each batch
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        try:
            total_cached = 0

            # Process entities in batches to avoid memory issues
            for i in range(0, len(entities), batch_size):
                batch = entities[i:i + batch_size]
                await self._cache_entities_batch_atomic(batch)
                total_cached += len(batch)

                # Small delay between batches to avoid overwhelming Redis
                if i + batch_size < len(entities):
                    await anyio.sleep(0.001)

            _LOGGER.info(f"Cached {total_cached} entities in {(len(entities) + batch_size - 1) // batch_size} batches")

        except Exception as e:
            _LOGGER.error(f"Failed to cache entities batch: {e}")
            raise

    async def _cache_entities_batch_atomic(self, entities: List[Dict[str, Any]]):
        """Cache a batch of entities atomically using pipeline with transaction"""
        if not entities:
            return

        # Use pipeline with transaction for atomicity
        async with self._redis.pipeline(transaction=True) as pipe:
            try:
                domains = set()
                cached_at = datetime.now(timezone.utc).isoformat()

                for entity_data in entities:
                    entity_id = entity_data.get("entity_id")
                    if not entity_id:
                        continue

                    domain = entity_id.split(".")[0]
                    domains.add(domain)

                    # Prepare data for Redis
                    cache_data = {
                        "entity_id": entity_id,
                        "state": entity_data.get("state", ""),
                        "domain": domain,
                        "friendly_name": entity_data.get("attributes", {}).get("friendly_name", entity_id),
                        "device_class": entity_data.get("attributes", {}).get("device_class", ""),
                        "area_name": entity_data.get("attributes", {}).get("area_name", ""),
                        "last_updated": entity_data.get("last_updated", ""),
                        "last_updated_ts": self._parse_timestamp(entity_data.get("last_updated", "")),
                        "attributes": entity_data.get("attributes", {}),
                        "cached_at": cached_at
                    }

                    # Add to pipeline
                    entity_key = f"{self._entity_prefix}{entity_id}"
                    domain_key = f"{self._domain_prefix}{domain}"

                    pipe.hset(entity_key, mapping=cache_data)
                    pipe.sadd(domain_key, entity_id)

                # Update metadata for all domains in the same transaction
                for domain in domains:
                    domain_key = f"{self._domain_prefix}{domain}"
                    pipe.scard(domain_key)

                # Execute all operations atomically
                results = await pipe.execute()

                # Update metadata with the counts from the transaction
                metadata_updates = {}
                count_results = results[-len(domains):]  # Last N results are the SCARD operations
                for domain, count in zip(domains, count_results):
                    metadata_updates[f"{domain}_count"] = count

                if metadata_updates:
                    await self._redis.hset(self._metadata_key, mapping=metadata_updates)

            except WatchError:
                _LOGGER.warning("Transaction was aborted due to watched key modification, retrying...")
                # Retry the operation
                await self._cache_entities_batch_atomic(entities)
    
    async def get_cached_entity(self, entity_id: str) -> Optional[Dict[str, Any]]:
        """Get cached entity data

        Args:
            entity_id: Entity ID to retrieve

        Returns:
            Cached entity data or None if not found
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        try:
            key = f"{self._entity_prefix}{entity_id}"
            data = await self._redis.hgetall(key)

            if not data:
                return None

            # Parse attributes back to dict
            if data.get("attributes"):
                try:
                    data["attributes"] = json.loads(data["attributes"])
                except json.JSONDecodeError:
                    data["attributes"] = {}

            # Convert numeric fields
            if data.get("last_updated_ts"):
                try:
                    data["last_updated_ts"] = float(data["last_updated_ts"])
                except (ValueError, TypeError):
                    data["last_updated_ts"] = 0.0

            return data

        except Exception as e:
            _LOGGER.error(f"Failed to get cached entity {entity_id}: {e}")
            return None

    async def get_cached_entities_batch(self, entity_ids: List[str]) -> Dict[str, Optional[Dict[str, Any]]]:
        """Get multiple cached entities in a single operation

        Args:
            entity_ids: List of entity IDs to retrieve

        Returns:
            Dictionary mapping entity_id to entity data (or None if not found)
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        if not entity_ids:
            return {}

        try:
            # Use pipeline for efficient batch retrieval
            async with self._redis.pipeline() as pipe:
                for entity_id in entity_ids:
                    key = f"{self._entity_prefix}{entity_id}"
                    pipe.hgetall(key)

                results = await pipe.execute()

            # Process results
            entities = {}
            for entity_id, data in zip(entity_ids, results):
                if data:
                    # Parse attributes back to dict
                    if data.get("attributes"):
                        try:
                            data["attributes"] = json.loads(data["attributes"])
                        except json.JSONDecodeError:
                            data["attributes"] = {}

                    # Convert numeric fields
                    if data.get("last_updated_ts"):
                        try:
                            data["last_updated_ts"] = float(data["last_updated_ts"])
                        except (ValueError, TypeError):
                            data["last_updated_ts"] = 0.0

                    entities[entity_id] = data
                else:
                    entities[entity_id] = None

            return entities

        except Exception as e:
            _LOGGER.error(f"Failed to get cached entities batch: {e}")
            return {entity_id: None for entity_id in entity_ids}
    
    async def search_entities(self, query: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Search entities using RediSearch

        Args:
            query: Search query
            limit: Maximum number of results
            offset: Offset for pagination

        Returns:
            List of matching entities
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        try:
            # Build search query with timeout
            search_query = Query(query).paging(offset, limit).timeout(5000)  # 5 second timeout

            # Execute search
            result = await self._redis.ft(self._index_name).search(search_query)

            entities = []
            for doc in result.docs:
                entity_data = dict(doc.__dict__)
                # Remove internal fields
                entity_data.pop("id", None)
                entity_data.pop("payload", None)

                # Parse attributes back to dict
                if entity_data.get("attributes"):
                    try:
                        entity_data["attributes"] = json.loads(entity_data["attributes"])
                    except json.JSONDecodeError:
                        entity_data["attributes"] = {}

                # Convert numeric fields
                if entity_data.get("last_updated_ts"):
                    try:
                        entity_data["last_updated_ts"] = float(entity_data["last_updated_ts"])
                    except (ValueError, TypeError):
                        entity_data["last_updated_ts"] = 0.0

                entities.append(entity_data)

            return entities

        except Exception as e:
            _LOGGER.error(f"Failed to search entities: {e}")
            return []
    
    async def get_entities_by_domain(self, domain: str) -> List[str]:
        """Get all entity IDs for a specific domain

        Args:
            domain: Domain name (e.g., 'light', 'sensor')

        Returns:
            List of entity IDs
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        try:
            entity_ids = await self._redis.smembers(f"{self._domain_prefix}{domain}")
            return list(entity_ids) if entity_ids else []

        except Exception as e:
            _LOGGER.error(f"Failed to get entities for domain {domain}: {e}")
            return []

    async def get_domains_with_counts(self) -> Dict[str, int]:
        """Get all domains with their entity counts

        Returns:
            Dictionary mapping domain names to entity counts
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        try:
            # Get all domain keys
            domain_keys = await self._redis.keys(f"{self._domain_prefix}*")

            if not domain_keys:
                return {}

            # Use pipeline to get all counts efficiently
            async with self._redis.pipeline() as pipe:
                for key in domain_keys:
                    pipe.scard(key)

                counts = await pipe.execute()

            # Build result dictionary
            result = {}
            for key, count in zip(domain_keys, counts):
                domain = key.replace(self._domain_prefix, "")
                if count > 0:  # Only include domains with entities
                    result[domain] = count

            return result

        except Exception as e:
            _LOGGER.error(f"Failed to get domains with counts: {e}")
            return {}
    
    async def remove_entity(self, entity_id: str):
        """Remove entity from cache using atomic operations

        Args:
            entity_id: Entity ID to remove
        """
        if not self._redis:
            raise HassClientError("Redis not connected")

        try:
            domain = entity_id.split(".")[0]

            # Use atomic Lua script for removal
            entity_key = f"{self._entity_prefix}{entity_id}"
            domain_key = f"{self._domain_prefix}{domain}"

            await self._execute_lua_script(
                'remove_entity',
                keys=[entity_key, domain_key, self._metadata_key],
                args=[entity_id, domain]
            )

            _LOGGER.debug(f"Removed entity from cache: {entity_id}")

        except Exception as e:
            _LOGGER.error(f"Failed to remove entity {entity_id}: {e}")

    async def remove_entities_batch(self, entity_ids: List[str]):
        """Remove multiple entities atomically

        Args:
            entity_ids: List of entity IDs to remove
        """
        if not self._redis or not entity_ids:
            return

        try:
            # Group entities by domain for efficient processing
            domains_entities = {}
            for entity_id in entity_ids:
                domain = entity_id.split(".")[0]
                if domain not in domains_entities:
                    domains_entities[domain] = []
                domains_entities[domain].append(entity_id)

            # Use pipeline with transaction for atomic batch removal
            async with self._redis.pipeline(transaction=True) as pipe:
                for domain, entities in domains_entities.items():
                    domain_key = f"{self._domain_prefix}{domain}"

                    # Remove all entities in this domain
                    for entity_id in entities:
                        entity_key = f"{self._entity_prefix}{entity_id}"
                        pipe.delete(entity_key)
                        pipe.srem(domain_key, entity_id)

                    # Get updated count for metadata
                    pipe.scard(domain_key)

                results = await pipe.execute()

                # Update metadata with new counts
                metadata_updates = {}
                count_index = 0
                for domain in domains_entities.keys():
                    # Find the SCARD result for this domain
                    # Each domain has 2*len(entities) + 1 operations (DEL, SREM for each entity, then SCARD)
                    entities_count = len(domains_entities[domain])
                    scard_index = count_index + 2 * entities_count
                    count = results[scard_index]

                    if count > 0:
                        metadata_updates[f"{domain}_count"] = count
                    else:
                        # Remove the count if no entities left
                        await self._redis.hdel(self._metadata_key, f"{domain}_count")

                    count_index = scard_index + 1

                if metadata_updates:
                    await self._redis.hset(self._metadata_key, mapping=metadata_updates)

            _LOGGER.info(f"Removed {len(entity_ids)} entities from cache")

        except Exception as e:
            _LOGGER.error(f"Failed to remove entities batch: {e}")
            raise
    
    def _parse_timestamp(self, timestamp_str: str) -> float:
        """Parse timestamp string to Unix timestamp
        
        Args:
            timestamp_str: ISO format timestamp string
            
        Returns:
            Unix timestamp as float
        """
        try:
            if not timestamp_str:
                return 0.0
            dt = datetime.fromisoformat(timestamp_str.replace("Z", "+00:00"))
            return dt.timestamp()
        except Exception:
            return 0.0
    
    def _update_metadata(self, domain: str):
        """Update metadata for domain

        Args:
            domain: Domain name
        """
        try:
            count = self._redis.scard(f"{self._domain_prefix}{domain}")
            self._redis.hset(
                self._metadata_key,
                f"{domain}_count",
                count
            )
        except Exception as e:
            _LOGGER.error(f"Failed to update metadata for domain {domain}: {e}")
    
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics
        
        Returns:
            Dictionary with cache statistics
        """
        if not self._redis:
            raise HassClientError("Redis not connected")
        
        try:
            # Get total entities
            total_keys = self._redis.eval(
                "return #redis.call('keys', ARGV[1])",
                0,
                f"{self._entity_prefix}*"
            )

            # Get domain counts
            metadata = self._redis.hgetall(self._metadata_key)

            # Get index info
            try:
                index_info = self._redis.ft(self._index_name).info()
                index_stats = {
                    "num_docs": index_info.get("num_docs", 0),
                    "max_doc_id": index_info.get("max_doc_id", 0),
                    "num_terms": index_info.get("num_terms", 0)
                }
            except:
                index_stats = {}

            memory_info = self._redis.info("memory")
            clients_info = self._redis.info("clients")

            return {
                "total_entities": total_keys,
                "domain_counts": metadata,
                "index_stats": index_stats,
                "redis_info": {
                    "used_memory": memory_info.get("used_memory_human", "unknown"),
                    "connected_clients": clients_info.get("connected_clients", 0)
                }
            }
            
        except Exception as e:
            _LOGGER.error(f"Failed to get cache stats: {e}")
            return {}

    async def __aenter__(self):
        """Async context manager entry"""
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.disconnect()
