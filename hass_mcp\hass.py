"""Home assistant api manager"""
import logging
import os
from typing import Dict, Any, List

import anyio
from aiohttp import ClientSession

from .decorators import retry_on_failure, log_errors
from .redis_manager import RedisManager
from .ws_client import HassClient


_LOGGER = logging.getLogger(__name__)


class HassManager:
    """Home Assistant api methods manager"""

    def __init__(self, url: str = None, token: str = None, redis_url: str = None):
        self.url = url
        self.token = token
        self._client = None
        self._session = None
        self._ssl_context = None
        self._shutdown_event = None
        self._headers = {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }

        # Initialize Redis manager
        self._redis_url = (
            redis_url or os.getenv("REDIS_URL", "redis://localhost:6379")
        )
        self._redis_manager = RedisManager(self._redis_url)
        self._event_subscription_id = None
    
    @property
    def shutdown(self) -> anyio.Event:
        """get shutdown event"""
        return self._shutdown_event
    
    @property
    def client(self) -> HassClient:
        """get WebSocket client"""
        return self._client

    @property
    def session(self) -> ClientSession:
        """get aiohttp session"""
        return self._session
    
    @retry_on_failure(max_retries=5, delay=5.0)
    async def connect(self):
        """connect to Home Assistant"""
        if self._client and self._client.connected:
            return

        self._shutdown_event = anyio.Event()

        # Connect to Redis first
        await self._redis_manager.connect()
        _LOGGER.info("Redis connected successfully")

        async with HassClient(self.url, self.token) as client:
            self._client = client
            self._session = self._client.get_session()
            self._ssl_context = self._client.get_ssl_context

            await self._populate_initial_cache()
            await self._subscribe_to_events()

            _LOGGER.info("HassManager Ready")
            await self._shutdown_event.wait()
            _LOGGER.info("HassManager Shutdown")

    @log_errors
    async def call_service(self, domain: str, service: str, data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Call service"""
        result = await self._client.call_service(domain=domain, service=service, service_data=data or {})
        return result or {"success": True}
    
    @log_errors
    async def get_history(self, entity_id: str, hours: int = 24) -> Dict[str, Any]:
        """Get history records"""
        from datetime import datetime, timedelta, timezone

        start_time = (datetime.now(timezone.utc) - timedelta(hours=hours)).isoformat()
        api_url = f"{self.url}/api/history/period/{start_time}"
        async with self._session.get(
            api_url, 
            headers=self._headers, 
            params={"filter_entity_id": entity_id},
            ssl=self._ssl_context,
        ) as response:
            assert response.status == 200
            history_data = await response.json()

            if not history_data or not history_data[0]:
                return {"states": [], "count": 0}
            
            states = history_data[0]
            return {"states": states, "count": len(states)}
    
    @log_errors
    async def get_error_log(self) -> Dict[str, Any]:
        """Get error log"""
        async with self._session.get(
            f"{self.url}/api/error_log", 
            headers=self._headers,
            ssl=self._ssl_context,
        ) as response:
            assert response.status == 200
            log_text = await response.text
            
            return {
                "log_text": log_text,
                "error_count": log_text.count("ERROR"),
                "warning_count": log_text.count("WARNING")
            }
    
    @log_errors
    async def get_overview(self) -> Dict[str, Any]:
        """Get system overview"""
        states = await self._client.get_states()
        
        # 按域名分組
        domains = {}
        for state in states:
            domain = state["entity_id"].split(".")[0]
            domains[domain] = domains.get(domain, 0) + 1
        
        return {
            "total_entities": len(states),
            "domains": dict(sorted(domains.items(), key=lambda x: x[1], reverse=True))
        }

    @log_errors
    async def get_entity_state(self, entity_id: str) -> Dict[str, Any]:
        """get entity state - first check cache, then fetch from HA"""
        cached_entity = await self._redis_manager.get_cached_entity(entity_id)
        if cached_entity:
            _LOGGER.debug(f"Retrieved entity {entity_id} from cache")
            return cached_entity

        # If not in cache, fetch from Home Assistant
        result = await self._client.get_entity_state(entity_id)
        if result:
            # Cache the result
            await self._redis_manager.cache_entity(result)

        return result

    @log_errors
    async def get_entities_by_domain(self, domain: str) -> List[str]:
        """Get all entity IDs for a specific domain

        Args:
            domain: Domain name (e.g., 'light', 'sensor')

        Returns:
            List of entity IDs
        """
        return await self._redis_manager.get_entities_by_domain(domain)

    @log_errors
    async def get_cache_stats(self) -> Dict[str, Any]:
        """Get Redis cache statistics

        Returns:
            Dictionary with cache statistics
        """
        return await self._redis_manager.get_cache_stats()
    
    @log_errors
    async def search_entities(self, query: str, limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """Search entities using RediSearch

        Args:
            query: Search query string
            limit: Maximum number of results
            offset: Offset for pagination

        Returns:
            List of matching entities
        """
        return await self._redis_manager.search_entities(query, limit, offset)

    @log_errors
    async def get_entities_batch(self, entity_ids: List[str]) -> Dict[str, Dict[str, Any]]:
        """Get multiple entities efficiently using batch retrieval

        Args:
            entity_ids: List of entity IDs to retrieve

        Returns:
            Dictionary mapping entity_id to entity data (None if not found)
        """
        # First try to get from cache
        cached_results = await self._redis_manager.get_cached_entities_batch(entity_ids)

        # Find entities not in cache
        missing_entities = [entity_id for entity_id, data in cached_results.items() if data is None]

        # Fetch missing entities from Home Assistant
        if missing_entities:
            _LOGGER.debug(
                f"Fetching {len(missing_entities)} entities from Home Assistant"
            )
            for entity_id in missing_entities:
                try:
                    entity_data = await self._client.get_entity_state(entity_id)
                    if entity_data:
                        cached_results[entity_id] = entity_data
                        # Cache the newly fetched entity
                        await self._redis_manager.cache_entity(entity_data)
                except Exception as e:
                    _LOGGER.warning(f"Failed to fetch entity {entity_id}: {e}")
                    cached_results[entity_id] = None

        return cached_results

    async def _populate_initial_cache(self):
        """Populate Redis cache with initial entity data"""
        try:
            _LOGGER.info("Populating initial entity cache...")
            states = await self._client.get_states()
            if states:
                await self._redis_manager.cache_entities_batch(states)
                _LOGGER.info(f"Cached {len(states)} entities initially")
        except Exception as e:
            _LOGGER.error(f"Failed to populate initial cache: {e}")

    async def _subscribe_to_events(self):
        """Subscribe to Home Assistant state change events"""
        try:
            self._event_subscription_id = await self._client.subscribe_events(
                event_type="state_changed",
                handler=self._handle_state_change_event
            )
            _LOGGER.info("Subscribed to state change events")
        except Exception as e:
            _LOGGER.error(f"Failed to subscribe to events: {e}")

    async def _handle_state_change_event(self, event_data: Dict[str, Any]):
        """Handle state change events and update cache"""
        try:
            # Extract event data structure
            data = event_data.get("data", {}) if isinstance(event_data, dict) else {}
            entity_id = data.get("entity_id")
            new_state = data.get("new_state")
            old_state = data.get("old_state")

            if not entity_id:
                _LOGGER.debug("Received state change event without entity_id")
                return

            if new_state:
                # Update cache with new state
                await self._redis_manager.cache_entity(new_state)
                _LOGGER.debug(
                    f"Updated cache for entity: {entity_id} - "
                    f"state: {new_state.get('state', 'unknown')}"
                )
            elif old_state and not new_state:
                # Entity was removed, remove from cache
                await self._redis_manager.remove_entity(entity_id)
                _LOGGER.debug(f"Removed entity from cache: {entity_id}")

        except Exception as e:
            _LOGGER.error(f"Failed to handle state change event: {e}")
            _LOGGER.debug(f"Event data: {event_data}")

    async def __aenter__(self):
        await self.connect()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Unsubscribe from events
        if self._event_subscription_id and self._client:
            try:
                await self._client.unsubscribe_events(self._event_subscription_id)
            except Exception as e:
                _LOGGER.error(f"Failed to unsubscribe from events: {e}")

        # Disconnect Redis
        await self._redis_manager.disconnect()

        # Shutdown Home Assistant connection
        if self._shutdown_event:
            await self._shutdown_event.set()