"""Home Assistant mcp server implementation."""
import logging
from contextlib import asynccontextmanager
from typing import List, Dict, Any

import anyio
from fastmcp import FastMCP

from .hass import HassManager


_LOGGER = logging.getLogger(__name__)


def create_mcp_server(ha_url: str, ha_token: str):
    """Create a FastMCP server for Home Assistant"""

    # Global HassManager instance
    hass_manager = None

    # Helper functions for common operations
    async def get_filtered_entities(domain=None, search_query=None, limit=None):
        """Get entities with optional filtering"""
        if not hass_manager:
            return None

        states = await hass_manager._client.get_states()
        if not states:
            return []

        # Filter by domain
        if domain:
            states = [
                state for state in states
                if state["entity_id"].startswith(f"{domain}.")
            ]

        # Filter by search query
        if search_query:
            filtered_states = []
            for state in states:
                entity_id = state.get("entity_id", "")
                friendly_name = state.get("attributes", {}).get(
                    "friendly_name", ""
                )
                if (search_query.lower() in entity_id.lower() or
                    search_query.lower() in friendly_name.lower()):
                    filtered_states.append(state)
            states = filtered_states

        # Apply limit
        if limit:
            states = states[:limit]

        return states

    # Lifespan management
    @asynccontextmanager
    async def hass_lifespan(server):
        """Simplified lifespan management for MCP server"""
        nonlocal hass_manager
        _LOGGER.info("Starting MCP server initialization...")

        try:
            hass_manager = HassManager(url=ha_url, token=ha_token)
            await hass_manager.connect()
            _LOGGER.info("MCP server initialization completed successfully")

            yield {"hass": hass_manager}
        except Exception as e:
            _LOGGER.error(f"Failed to initialize MCP server: {e}")
            raise
        finally:
            if hass_manager:
                await hass_manager.__aexit__(None, None, None)
            await anyio.sleep(1)
            _LOGGER.info("MCP server shutdown completed")

    mcp_server = FastMCP(
        name="Hass-MCP",
        instructions=(
            "A Model Context Protocol server for Home Assistant integration "
            "with LLMs using WebSockets."
        ),
        lifespan=hass_lifespan
    )

    # Add Redis search tools
    @mcp_server.tool()
    async def redis_search_entities(query: str, limit: int = 50, offset: int = 0) -> Dict[str, Any]:
        """
        Search entities using RediSearch for fast full-text search

        Args:
            query: Search query (supports RediSearch syntax)
            limit: Maximum number of results (default: 50)
            offset: Offset for pagination (default: 0)

        Returns:
            Dictionary with search results and metadata

        Examples:
            query="light" - find all lights
            query="@domain:sensor @state:on" - find sensors that are on
            query="kitchen" - find entities with "kitchen" in name or attributes
            query="@friendly_name:living*" - find entities with friendly names starting with "living"
        """
        _LOGGER.info(f"Redis search: '{query}' (limit: {limit}, offset: {offset})")
        try:
            if not hass_manager:
                return {
                    "query": query,
                    "results": [],
                    "count": 0,
                    "limit": limit,
                    "offset": offset,
                    "error": "Home Assistant not connected"
                }

            # Use the optimized search method
            results = await hass_manager.search_entities(query, limit, offset)

            return {
                "query": query,
                "results": results,
                "count": len(results),
                "limit": limit,
                "offset": offset
            }
        except Exception as e:
            _LOGGER.error(f"Redis search failed: {e}")
            return {"error": str(e), "results": [], "count": 0}

    @mcp_server.tool()
    async def get_entities_batch_tool(entity_ids: List[str]) -> Dict[str, Any]:
        """
        Get multiple entities efficiently using batch retrieval

        Args:
            entity_ids: List of entity IDs to retrieve

        Returns:
            Dictionary with entity data for each requested entity

        Examples:
            entity_ids=["light.living_room", "sensor.temperature", "switch.fan"]
        """
        _LOGGER.info(f"Batch retrieving {len(entity_ids)} entities")
        try:
            if not hass_manager:
                return {"error": "Home Assistant not connected"}

            if not entity_ids:
                return {"entities": {}, "count": 0}

            # Use the optimized batch method
            results = await hass_manager.get_entities_batch(entity_ids)

            # Filter out None results for cleaner response
            valid_results = {k: v for k, v in results.items() if v is not None}
            missing_entities = [k for k, v in results.items() if v is None]

            response = {
                "entities": valid_results,
                "count": len(valid_results),
                "requested": len(entity_ids)
            }

            if missing_entities:
                response["missing"] = missing_entities
                response["missing_count"] = len(missing_entities)

            return response

        except Exception as e:
            _LOGGER.error(f"Batch entity retrieval failed: {e}")
            return {"error": str(e)}

    @mcp_server.tool()
    async def get_entities_by_domain_cached(domain: str) -> Dict[str, Any]:
        """
        Get all entity IDs for a domain from Redis cache (very fast)

        Args:
            domain: Domain name (e.g., 'light', 'sensor', 'switch')

        Returns:
            Dictionary with entity IDs and count

        Examples:
            domain="light" - get all light entity IDs
            domain="sensor" - get all sensor entity IDs
        """
        _LOGGER.info(f"Getting cached entities for domain: {domain}")
        try:
            # This will be implemented when HassManager is properly integrated
            return {
                "domain": domain,
                "entity_ids": [],
                "count": 0,
                "note": "Domain cache will be available when Redis integration is complete"
            }
        except Exception as e:
            _LOGGER.error(f"Failed to get entities for domain {domain}: {e}")
            return {"error": str(e), "entity_ids": [], "count": 0}

    @mcp_server.tool()
    async def get_cache_statistics() -> Dict[str, Any]:
        """
        Get Redis cache statistics and performance metrics

        Returns:
            Dictionary with cache statistics including:
            - total_entities: Total number of cached entities
            - domain_counts: Count of entities per domain
            - index_stats: RediSearch index statistics
            - redis_info: Redis server information
        """
        _LOGGER.info("Getting cache statistics")
        try:
            if not hass_manager:
                return {"error": "Home Assistant not connected"}

            # Use the optimized cache stats method
            stats = await hass_manager._redis_manager.get_cache_stats()

            return {
                "total_entities": stats.get("total_entities", 0),
                "domain_counts": stats.get("domain_counts", {}),
                "redis_info": stats.get("redis_info", {}),
                "cache_healthy": bool(stats)
            }
        except Exception as e:
            _LOGGER.error(f"Failed to get cache statistics: {e}")
            return {"error": str(e)}

    @mcp_server.tool()
    async def get_version() -> str:
        """
        Get the Home Assistant version

        Returns:
            A string with the Home Assistant version (e.g., "2025.3.0")
        """
        _LOGGER.info("Getting Home Assistant version")
        if hass_manager:
            config = await hass_manager.get_config()
            return config.get("version", "unknown")
        return "Home Assistant not connected"

    @mcp_server.tool()
    async def get_entity(entity_id: str, fields: List[str] = [], detailed: bool = False) -> dict:
        """
        Get the state of a Home Assistant entity with optional field filtering

        Args:
            entity_id: The entity ID to get (e.g. 'light.living_room')
            fields: List of fields to include (e.g. ['state', 'attr.brightness']). Leave empty for default.
            detailed: If True, returns all entity fields without filtering

        Examples:
            entity_id="light.living_room" - basic state check
            entity_id="light.living_room", fields=["state", "attr.brightness"] - specific fields
            entity_id="light.living_room", detailed=True - all details
        """
        _LOGGER.info(f"Getting entity state: {entity_id}")
        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            entity_data = await hass_manager.get_entity_state(entity_id)
            if not entity_data:
                return {"error": f"Entity {entity_id} not found"}

            if detailed:
                return entity_data
            elif fields:
                # Filter to specific fields
                result = {}
                for field in fields:
                    if field.startswith("attr."):
                        attr_name = field[5:]  # Remove "attr." prefix
                        result[field] = entity_data.get("attributes", {}).get(attr_name)
                    else:
                        result[field] = entity_data.get(field)
                return result
            else:
                # Return lean format
                return {
                    "entity_id": entity_data.get("entity_id"),
                    "state": entity_data.get("state"),
                    "friendly_name": entity_data.get("attributes", {}).get("friendly_name"),
                    "last_updated": entity_data.get("last_updated")
                }
        except Exception as e:
            _LOGGER.error(f"Failed to get entity {entity_id}: {e}")
            return {"error": str(e)}

    @mcp_server.tool()
    async def entity_action(entity_id: str, action: str, params: Dict[str, Any] = {}) -> dict:
        """
        Perform an action on a Home Assistant entity (on, off, toggle)
        
        Args:
            entity_id: The entity ID to control (e.g. 'light.living_room')
            action: The action to perform ('on', 'off', 'toggle')
            params: params: Dictionary of additional parameters for the service call
        
        Returns:
            The response from Home Assistant
        
        Examples:
            entity_id="light.living_room", action="on", params={"brightness": 255}
            entity_id="switch.garden_lights", action="off"
            entity_id="climate.living_room", action="on", params={"temperature": 22.5}
        
        Domain-Specific Parameters:
            - Lights: brightness (0-255), color_temp, rgb_color, transition, effect
            - Covers: position (0-100), tilt_position
            - Climate: temperature, target_temp_high, target_temp_low, hvac_mode
            - Media players: source, volume_level (0-1)
        """
        if action not in ["on", "off", "toggle"]:
            return {"error": f"Invalid action: {action}. Valid actions are 'on', 'off', 'toggle'"}
        
        # Map action to service name
        service = action if action == "toggle" else f"turn_{action}"
        
        # Extract the domain from the entity_id
        domain = entity_id.split(".")[0]
        
        # Prepare service data
        data = {"entity_id": entity_id, **(params or {})}
        
        _LOGGER.info(f"Performing action '{action}' on entity: {entity_id} with params: {params}")
        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            result = await hass_manager.call_service(domain, service, data)
            return result
        except Exception as e:
            _LOGGER.error(f"Failed to perform action {action} on {entity_id}: {e}")
            return {"error": str(e)}


    @mcp_server.tool()
    async def list_entities(
        domain: str = "", 
        search_query: str = "", 
        limit: int = 100,
        fields: List[str] = [],
        detailed: bool = False
    ) -> List[Dict[str, Any]]:
        """
        Get a list of Home Assistant entities with optional filtering
        
        Args:
            domain: Domain to filter by (e.g., 'light', 'switch'). Leave empty for all domains.
            search_query: Search term to filter entities by name, id, or attributes. Leave empty for no search.
                        (Note: Does not support wildcards. To get all entities, leave this empty)
            limit: Maximum number of entities to return (default: 100)
            fields: Optional list of specific fields to include in each entity
            detailed: If True, returns all entity fields without filtering
        
        Returns:
            A list of entity dictionaries with lean formatting by default
        
        Examples:
            domain="light" - get all lights
            search_query="kitchen", limit=20 - search entities
            domain="sensor", detailed=True - full sensor details
        
        Best Practices:
            - Use lean format (default) for most operations
            - Prefer domain filtering over no filtering
            - For domain overviews, use domain_summary_tool instead of list_entities
            - Only request detailed=True when necessary for full attribute inspection
            - To get all entity types/domains, use list_entities without a domain filter, 
            then extract domains from entity_ids
        """
        log_message = "Getting entities"
        if domain:
            log_message += f" for domain: {domain}"
        if search_query:
            log_message += f" matching: '{search_query}'"
        if limit != 100:
            log_message += f" (limit: {limit})"
        if detailed:
            log_message += " (detailed format)"
        elif fields:
            log_message += f" (custom fields: {fields})"
        else:
            log_message += " (lean format)"
        
        _LOGGER.info(log_message)
        
        # Handle special case where search_query is a wildcard/asterisk - just ignore it
        if search_query == "*":
            search_query = None
            _LOGGER.info("Converting '*' search query to None (retrieving all entities)")
        
        # Use helper function to get filtered entities
        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            states = await get_filtered_entities(domain=domain, search_query=search_query, limit=limit)
            if states is None:
                return {"error": "Home Assistant not connected"}
            if not states:
                return []

            # Format results based on detailed flag and fields
            if detailed:
                return states
            elif fields:
                result = []
                for state in states:
                    entity_result = {}
                    for field in fields:
                        if field.startswith("attr."):
                            attr_name = field[5:]  # Remove "attr." prefix
                            entity_result[field] = state.get("attributes", {}).get(attr_name)
                        else:
                            entity_result[field] = state.get(field)
                    result.append(entity_result)
                return result
            else:
                # Return lean format
                result = []
                for state in states:
                    result.append({
                        "entity_id": state.get("entity_id"),
                        "state": state.get("state"),
                        "friendly_name": state.get("attributes", {}).get("friendly_name"),
                        "last_updated": state.get("last_updated")
                    })
                return result
        except Exception as e:
            _LOGGER.error(f"Failed to get entities: {e}")
            return {"error": str(e)}


    @mcp_server.tool()
    async def search_entities_tool(query: str, limit: int = 20) -> Dict[str, Any]:
        """
        Search for entities matching a query string
        
        Args:
            query: The search query to match against entity IDs, names, and attributes.
                (Note: Does not support wildcards. To get all entities, leave this blank or use list_entities tool)
            limit: Maximum number of results to return (default: 20)
        
        Returns:
            A dictionary containing search results and metadata:
            - count: Total number of matching entities found
            - results: List of matching entities with essential information
            - domains: Map of domains with counts (e.g. {"light": 3, "sensor": 2})
            
        Examples:
            query="temperature" - find temperature entities
            query="living room", limit=10 - find living room entities
            query="", limit=500 - list all entity types
            
        """
        _LOGGER.info(f"Searching for entities matching: '{query}' with limit: {limit}")
        
        # Special case - treat "*" as empty query to just return entities without filtering
        if query == "*":
            query = ""
            _LOGGER.info("Converting '*' to empty query (retrieving all entities up to limit)")
        
        # Handle empty query as a special case to just return entities up to the limit
        if not query or not query.strip():
            _LOGGER.info(f"Empty query - retrieving up to {limit} entities without filtering")
            try:
                entities = await get_filtered_entities(limit=limit)
                if entities is None:
                    return {"error": "Home Assistant not connected", "count": 0, "results": [], "domains": {}}
            except Exception as e:
                return {"error": str(e), "count": 0, "results": [], "domains": {}}
            
            # Check if there was an error
            if isinstance(entities, dict) and "error" in entities:
                return {"error": entities["error"], "count": 0, "results": [], "domains": {}}
            
            # No query, but we'll return a structured result anyway
            domains_count = {}
            simplified_entities = []
            
            for entity in entities:
                domain = entity["entity_id"].split(".")[0]
                
                # Count domains
                if domain not in domains_count:
                    domains_count[domain] = 0
                domains_count[domain] += 1
                
                # Create simplified entity representation
                simplified_entity = {
                    "entity_id": entity["entity_id"],
                    "state": entity["state"],
                    "domain": domain,
                    "friendly_name": entity.get("attributes", {}).get("friendly_name", entity["entity_id"])
                }
                
                # Add key attributes based on domain
                attributes = entity.get("attributes", {})
                
                # Include domain-specific important attributes
                if domain == "light" and "brightness" in attributes:
                    simplified_entity["brightness"] = attributes["brightness"]
                elif domain == "sensor" and "unit_of_measurement" in attributes:
                    simplified_entity["unit"] = attributes["unit_of_measurement"]
                elif domain == "climate" and "temperature" in attributes:
                    simplified_entity["temperature"] = attributes["temperature"]
                elif domain == "media_player" and "media_title" in attributes:
                    simplified_entity["media_title"] = attributes["media_title"]
                
                simplified_entities.append(simplified_entity)
            
            # Return structured response for empty query
            return {
                "count": len(simplified_entities),
                "results": simplified_entities,
                "domains": domains_count,
                "query": "all entities (no filtering)"
            }
        
        # Normal search with non-empty query
        try:
            entities = await get_filtered_entities(search_query=query, limit=limit)
            if entities is None:
                return {"error": "Home Assistant not connected", "count": 0, "results": [], "domains": {}}
        except Exception as e:
            return {"error": str(e), "count": 0, "results": [], "domains": {}}
        
        # Check if there was an error
        if isinstance(entities, dict) and "error" in entities:
            return {"error": entities["error"], "count": 0, "results": [], "domains": {}}
        
        # Prepare the results
        domains_count = {}
        simplified_entities = []
        
        for entity in entities:
            domain = entity["entity_id"].split(".")[0]
            
            # Count domains
            if domain not in domains_count:
                domains_count[domain] = 0
            domains_count[domain] += 1
            
            # Create simplified entity representation
            simplified_entity = {
                "entity_id": entity["entity_id"],
                "state": entity["state"],
                "domain": domain,
                "friendly_name": entity.get("attributes", {}).get("friendly_name", entity["entity_id"])
            }
            
            # Add key attributes based on domain
            attributes = entity.get("attributes", {})
            
            # Include domain-specific important attributes
            if domain == "light" and "brightness" in attributes:
                simplified_entity["brightness"] = attributes["brightness"]
            elif domain == "sensor" and "unit_of_measurement" in attributes:
                simplified_entity["unit"] = attributes["unit_of_measurement"]
            elif domain == "climate" and "temperature" in attributes:
                simplified_entity["temperature"] = attributes["temperature"]
            elif domain == "media_player" and "media_title" in attributes:
                simplified_entity["media_title"] = attributes["media_title"]
            
            simplified_entities.append(simplified_entity)
        
        # Return structured response
        return {
            "count": len(simplified_entities),
            "results": simplified_entities,
            "domains": domains_count,
            "query": query
        }
        
   
    # The domain_summary_tool is already implemented, no need to duplicate it
    @mcp_server.tool()
    async def domain_summary_tool(domain: str, example_limit: int = 3) -> Dict[str, Any]:
        """
        Get a summary of entities in a specific domain
        
        Args:
            domain: The domain to summarize (e.g., 'light', 'switch', 'sensor')
            example_limit: Maximum number of examples to include for each state
        
        Returns:
            A dictionary containing:
            - total_count: Number of entities in the domain
            - state_distribution: Count of entities in each state
            - examples: Sample entities for each state
            - common_attributes: Most frequently occurring attributes
            
        Examples:
            domain="light" - get light summary
            domain="climate", example_limit=5 - climate summary with more examples
        Best Practices:
            - Use this before retrieving all entities in a domain to understand what's available    """
        _LOGGER.info(f"Getting domain summary for: {domain}")
        try:
            domain_states = await get_filtered_entities(domain=domain)
            if domain_states is None:
                return {"error": "Home Assistant not connected"}
            if not domain_states:
                return {"total_count": 0, "state_distribution": {}, "examples": {}, "common_attributes": {}}

            if not domain_states:
                return {"total_count": 0, "state_distribution": {}, "examples": {}, "common_attributes": {}}

            # Count states
            state_distribution = {}
            examples = {}
            for state in domain_states:
                state_value = state.get("state", "unknown")
                if state_value not in state_distribution:
                    state_distribution[state_value] = 0
                    examples[state_value] = []
                state_distribution[state_value] += 1

                # Add examples (limited by example_limit)
                if len(examples[state_value]) < example_limit:
                    examples[state_value].append({
                        "entity_id": state.get("entity_id"),
                        "friendly_name": state.get("attributes", {}).get("friendly_name"),
                        "state": state_value
                    })

            # Get common attributes
            all_attributes = {}
            for state in domain_states:
                for attr_name in state.get("attributes", {}):
                    if attr_name not in all_attributes:
                        all_attributes[attr_name] = 0
                    all_attributes[attr_name] += 1

            # Sort by frequency and take top 10
            common_attributes = dict(sorted(all_attributes.items(), key=lambda x: x[1], reverse=True)[:10])

            return {
                "total_count": len(domain_states),
                "state_distribution": state_distribution,
                "examples": examples,
                "common_attributes": common_attributes
            }
        except Exception as e:
            _LOGGER.error(f"Failed to get domain summary: {e}")
            return {"error": str(e)}

    @mcp_server.tool()
    async def system_overview() -> Dict[str, Any]:
        """
        Get a comprehensive overview of the entire Home Assistant system
        
        Returns:
            A dictionary containing:
            - total_entities: Total count of all entities
            - domains: Dictionary of domains with their entity counts and state distributions
            - domain_samples: Representative sample entities for each domain (2-3 per domain)
            - domain_attributes: Common attributes for each domain
            - area_distribution: Entities grouped by area (if available)
            
        Examples:
            Returns domain counts, sample entities, and common attributes
        Best Practices:
            - Use this as the first call when exploring an unfamiliar Home Assistant instance
            - Perfect for building context about the structure of the smart home
            - After getting an overview, use domain_summary_tool to dig deeper into specific domains
        """
        _LOGGER.info("Generating complete system overview")
        try:
            states = await get_filtered_entities()
            if states is None:
                return {"error": "Home Assistant not connected"}
            if not states:
                return {"total_entities": 0, "domains": {}, "summary": "No entities found"}

            # Group by domain
            domains = {}
            for state in states:
                entity_id = state.get("entity_id", "")
                domain = entity_id.split(".")[0] if "." in entity_id else "unknown"

                if domain not in domains:
                    domains[domain] = {"count": 0, "states": {}}

                domains[domain]["count"] += 1

                # Count states
                state_value = state.get("state", "unknown")
                if state_value not in domains[domain]["states"]:
                    domains[domain]["states"][state_value] = 0
                domains[domain]["states"][state_value] += 1

            # Sort domains by entity count
            sorted_domains = dict(sorted(domains.items(), key=lambda x: x[1]["count"], reverse=True))

            return {
                "total_entities": len(states),
                "domains": sorted_domains,
                "summary": f"Found {len(states)} entities across {len(domains)} domains"
            }
        except Exception as e:
            _LOGGER.error(f"Failed to get system overview: {e}")
            return {"error": str(e)}


    # Automation management MCP tools
    @mcp_server.tool()
    async def list_automations() -> List[Dict[str, Any]]:
        """
        Get a list of all automations from Home Assistant
        
        This function retrieves all automations configured in Home Assistant,
        including their IDs, entity IDs, state, and display names.
        
        Returns:
            A list of automation dictionaries, each containing id, entity_id, 
            state, and alias (friendly name) fields.
            
        Examples:
            Returns all automation objects with state and friendly names
        
        """
        _LOGGER.info("Getting all automations")
        try:
            # Get automation entities using helper function
            automations = await get_filtered_entities(domain="automation")
            if automations is None:
                return {"error": "Home Assistant not connected"}
            if not automations:
                return []
            
            # Handle error responses that might still occur
            if isinstance(automations, dict) and "error" in automations:
                _LOGGER.warning(f"Error getting automations: {automations['error']}")
                return []
                
            # Handle case where response is a list with error
            if isinstance(automations, list) and len(automations) == 1 and isinstance(automations[0], dict) and "error" in automations[0]:
                _LOGGER.warning(f"Error getting automations: {automations[0]['error']}")
                return []
                
            return automations
        except Exception as e:
            _LOGGER.error(f"Error in list_automations: {str(e)}")
            return []

    # We already have a list_automations tool, so no need to duplicate functionality

    @mcp_server.tool()
    async def restart_ha() -> Dict[str, Any]:
        """
        Restart Home Assistant
        
        ⚠️ WARNING: Temporarily disrupts all Home Assistant operations
        
        Returns:
            Result of restart operation
        """
        _LOGGER.info("Restarting Home Assistant")
        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            # Call the restart service
            result = await hass_manager._client.call_service("homeassistant", "restart")
            return {"success": True, "message": "Home Assistant restart initiated", "result": result}
        except Exception as e:
            _LOGGER.error(f"Failed to restart Home Assistant: {e}")
            return {"error": str(e)}

    @mcp_server.tool()
    async def call_service_tool(domain: str, service: str, data: Dict[str, Any] = {}) -> Dict[str, Any]:
        """
        Call any Home Assistant service (low-level API access)
        
        Args:
            domain: The domain of the service (e.g., 'light', 'switch', 'automation')
            service: The service to call (e.g., 'turn_on', 'turn_off', 'toggle')
            data: Data to pass to the service (e.g., {'entity_id': 'light.living_room'})
        
        Returns:
            The response from Home Assistant (usually empty for successful calls)
        
        Examples:
            domain='light', service='turn_on', data={'entity_id': 'light.x', 'brightness': 255}
            domain='automation', service='reload'
            domain='fan', service='set_percentage', data={'entity_id': 'fan.x', 'percentage': 50}
        
        """
        _LOGGER.info(f"Calling Home Assistant service: {domain}.{service} with data: {data}")
        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            result = await hass_manager._client.call_service(domain, service, data)
            return {"success": True, "result": result}
        except Exception as e:
            _LOGGER.error(f"Failed to call service {domain}.{service}: {e}")
            return {"error": str(e)}

    # Prompt functionality
    @mcp_server.prompt()
    def create_automation(trigger_type: str, entity_id: str = ""):
        """
        Guide a user through creating a Home Assistant automation
        
        This prompt provides a step-by-step guided conversation for creating
        a new automation in Home Assistant based on the specified trigger type.
        
        Args:
            trigger_type: The type of trigger for the automation (state, time, etc.)
            entity_id: Optional entity to use as the trigger source
        
        Returns:
            A list of messages for the interactive conversation
        """
        # Define the initial system message
        system_message = """You are an automation creation assistant for Home Assistant.
    You'll guide the user through creating an automation with the following steps:
    1. Define the trigger conditions based on their specified trigger type
    2. Specify the actions to perform
    3. Add any conditions (optional)
    4. Review and confirm the automation"""
        
        # Define the first user message based on parameters
        trigger_description = {
            "state": "an entity changing state",
            "time": "a specific time of day",
            "numeric_state": "a numeric value crossing a threshold",
            "zone": "entering or leaving a zone",
            "sun": "sun events (sunrise/sunset)",
            "template": "a template condition becoming true"
        }
        
        description = trigger_description.get(trigger_type, trigger_type)
        
        if entity_id:
            user_message = f"I want to create an automation triggered by {description} for {entity_id}."
        else:
            user_message = f"I want to create an automation triggered by {description}."
        
        # Return the conversation starter messages
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

    @mcp_server.prompt()
    def debug_automation(automation_id: str):
        """
        Help a user troubleshoot an automation that isn't working
        
        This prompt guides the user through the process of diagnosing and fixing
        issues with an existing Home Assistant automation.
        
        Args:
            automation_id: The entity ID of the automation to troubleshoot
        
        Returns:
            A list of messages for the interactive conversation
        """
        system_message = """You are a Home Assistant automation troubleshooting expert.
    You'll help the user diagnose problems with their automation by checking:
    1. Trigger conditions and whether they're being met
    2. Conditions that might be preventing execution
    3. Action configuration issues
    4. Entity availability and connectivity
    5. Permissions and scope issues"""
        
        user_message = f"My automation {automation_id} isn't working properly. Can you help me troubleshoot it?"
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

    @mcp_server.prompt()
    def troubleshoot_entity(entity_id: str):
        """
        Guide a user through troubleshooting issues with an entity
        
        This prompt helps diagnose and resolve problems with a specific
        Home Assistant entity that isn't functioning correctly.
        
        Args:
            entity_id: The entity ID having issues
        
        Returns:
            A list of messages for the interactive conversation
        """
        system_message = """You are a Home Assistant entity troubleshooting expert.
    You'll help the user diagnose problems with their entity by checking:
    1. Entity status and availability
    2. Integration status
    3. Device connectivity
    4. Recent state changes and error patterns
    5. Configuration issues
    6. Common problems with this entity type"""
        
        user_message = f"My entity {entity_id} isn't working properly. Can you help me troubleshoot it?"
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

    @mcp_server.prompt()
    def routine_optimizer():
        """
        Analyze usage patterns and suggest optimized routines based on actual behavior
        
        This prompt helps users analyze their Home Assistant usage patterns and create
        more efficient routines, automations, and schedules based on real usage data.
        
        Returns:
            A list of messages for the interactive conversation
        """
        system_message = """You are a Home Assistant optimization expert specializing in routine analysis.
    You'll help the user analyze their usage patterns and create optimized routines by:
    1. Reviewing entity state histories to identify patterns
    2. Analyzing when lights, climate controls, and other devices are used
    3. Finding correlations between different device usages
    4. Suggesting automations based on detected routines
    5. Optimizing existing automations to better match actual usage
    6. Creating schedules that adapt to the user's lifestyle
    7. Identifying energy-saving opportunities based on usage patterns"""
        
        user_message = "I'd like to optimize my home automations based on my actual usage patterns. Can you help analyze how I use my smart home and suggest better routines?"
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

    @mcp_server.prompt()
    def automation_health_check():
        """
        Review all automations, find conflicts, redundancies, or improvement opportunities
        
        This prompt helps users perform a comprehensive review of their Home Assistant
        automations to identify issues, optimize performance, and improve reliability.
        
        Returns:
            A list of messages for the interactive conversation
        """
        system_message = """You are a Home Assistant automation expert specializing in system optimization.
    You'll help the user perform a comprehensive audit of their automations by:
    1. Reviewing all automations for potential conflicts (e.g., opposing actions)
    2. Identifying redundant automations that could be consolidated
    3. Finding inefficient trigger patterns that might cause unnecessary processing
    4. Detecting missing conditions that could improve reliability
    5. Suggesting template optimizations for more efficient processing
    6. Uncovering potential race conditions between automations
    7. Recommending structural improvements to the automation organization
    8. Highlighting best practices and suggesting implementation changes"""
        
        user_message = "I'd like to do a health check on all my Home Assistant automations. Can you help me review them for conflicts, redundancies, and potential improvements?"
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

    @mcp_server.prompt()
    def entity_naming_consistency():
        """
        Audit entity names and suggest standardization improvements
        
        This prompt helps users analyze their entity naming conventions and create
        a more consistent, organized naming system across their Home Assistant instance.
        
        Returns:
            A list of messages for the interactive conversation
        """
        system_message = """You are a Home Assistant organization expert specializing in entity naming conventions.
    You'll help the user audit and improve their entity naming by:
    1. Analyzing current entity IDs and friendly names for inconsistencies
    2. Identifying patterns in existing naming conventions
    3. Suggesting standardized naming schemes based on entity types and locations
    4. Creating clear guidelines for future entity naming
    5. Proposing specific name changes for entities that don't follow conventions
    6. Showing how to implement these changes without breaking automations
    7. Explaining benefits of consistent naming for automation and UI organization"""
        
        user_message = "I'd like to make my Home Assistant entity names more consistent and organized. Can you help me audit my current naming conventions and suggest improvements?"
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

    @mcp_server.prompt()
    def dashboard_layout_generator():
        """
        Create optimized dashboards based on user preferences and usage patterns
        
        This prompt helps users design effective, user-friendly dashboards
        for their Home Assistant instance based on their specific needs.
        
        Returns:
            A list of messages for the interactive conversation
        """
        system_message = """You are a Home Assistant UI design expert specializing in dashboard creation.
    You'll help the user create optimized dashboards by:
    1. Analyzing which entities they interact with most frequently
    2. Identifying logical groupings of entities (by room, function, or use case)
    3. Suggesting dashboard layouts with the most important controls prominently placed
    4. Creating specialized views for different contexts (mobile, tablet, wall-mounted)
    5. Designing intuitive card arrangements that minimize scrolling/clicking
    6. Recommending specialized cards and custom components that enhance usability
    7. Balancing information density with visual clarity
    8. Creating consistent visual patterns that aid in quick recognition"""
        
        user_message = "I'd like to redesign my Home Assistant dashboards to be more functional and user-friendly. Can you help me create optimized layouts based on how I actually use my system?"
        
        return [
            {"role": "system", "content": system_message},
            {"role": "user", "content": user_message}
        ]

    # Documentation endpoint
    @mcp_server.tool()
    async def get_history(entity_id: str, hours: int = 24) -> Dict[str, Any]:
        """
        Get the history of an entity's state changes
        
        Args:
            entity_id: The entity ID to get history for
            hours: Number of hours of history to retrieve (default: 24)
        
        Returns:
            A dictionary containing:
            - entity_id: The entity ID requested
            - states: List of state objects with timestamps
            - count: Number of state changes found
            - first_changed: Timestamp of earliest state change
            - last_changed: Timestamp of most recent state change
            
        Examples:
            entity_id="light.living_room" - get 24h history
            entity_id="sensor.temperature", hours=168 - get 7 day history
        Best Practices:
            - Keep hours reasonable (24-72) for token efficiency
            - Use for entities with discrete state changes rather than continuously changing sensors
            - Consider the state distribution rather than every individual state    
        """
        _LOGGER.info(f"Getting history for entity: {entity_id}, hours: {hours}")
        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            # Get entity history from Home Assistant
            from datetime import datetime, timedelta, timezone

            # Calculate start time
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=hours)

            # Call history API
            history_result = await hass_manager._client.get_history(
                start_time=start_time.isoformat(),
                end_time=end_time.isoformat(),
                entity_ids=[entity_id]
            )
        except Exception as e:
            _LOGGER.error(f"Failed to get entity history: {e}")
            return {"error": str(e)}
        
        # Add the entity_id to the final response for context
        response = {
            "entity_id": entity_id,
            **history_result
        }
        return response

    @mcp_server.tool()
    async def get_error_log() -> Dict[str, Any]:
        """
        Get the Home Assistant error log for troubleshooting

        Returns:
            A dictionary containing:
            - log_text: The full error log text
            - error_count: Number of ERROR entries found
            - warning_count: Number of WARNING entries found
            - integration_mentions: Map of integration names to mention counts
            - error: Error message if retrieval failed

        Examples:
            Returns errors, warnings count and integration mentions
        Best Practices:
            - Use this tool when troubleshooting specific Home Assistant errors
            - Look for patterns in repeated errors
            - Pay attention to timestamps to correlate errors with events
            - Focus on integrations with many mentions in the log
        """
        _LOGGER.info("Getting Home Assistant error log")
        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            # Get error log from Home Assistant
            await hass_manager._client.call_service("system_log", "write", {
                "message": "MCP Server requesting error log",
                "level": "info"
            })

            # Note: Home Assistant doesn't have a direct API to get error logs
            # This would typically require file system access or a custom integration
            return {
                "message": "Error log access requires file system permissions or custom integration",
                "suggestion": "Check Home Assistant logs through the UI or log files directly"
            }
        except Exception as e:
            _LOGGER.error(f"Failed to access error log: {e}")
            return {"error": str(e)}



    return mcp_server


__all__ = ["create_mcp_server"]
