"""Hass MCP decorators."""
import functools
import logging
from typing import (
    Any, Callable, TypeVar,
    Optional, Awaitable,
)

import anyio

from .exceptions import (
    ConnectionError,
)

_LOGGER = logging.getLogger(__name__)

F = TypeVar('F', bound=Callable[..., Awaitable[Any]])


def require_connection(func: F) -> F:
    """Decorator: Ensure connection is established"""
    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        if not self.connected:
            raise ConnectionError("Not connected")
        return await func(self, *args, **kwargs)
    return wrapper


def log_errors(func: F = None, *, logger: Optional[logging.Logger] = None):
    """Decorator: Log errors"""
    def decorator(f: F) -> F:
        @functools.wraps(f)
        async def wrapper(self, *args, **kwargs):
            try:
                return await f(self, *args, **kwargs)
            except Exception as e:
                log = logger or _LOGGER
                log.error(f"Error in {f.__name__}: {e}")
                return {"error": str(e)}
        return wrapper

    if func is None:
        # Called with arguments: @log_errors(logger=...)
        return decorator
    else:
        # Called without arguments: @log_errors
        return decorator(func)


def with_timeout(timeout: float = 10.0):
    """Decorator: Add timeout handling to methods"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            try:
                with anyio.fail_after(timeout):
                    return await func(self, *args, **kwargs)
            except TimeoutError:
                raise TimeoutError(
                    f"{func.__name__} timeout ({timeout}s)"
                )
        return wrapper
    return decorator


def retry_on_failure(
    max_retries: int = 5,
    delay: float = 5.0,
):
    """Decorator: Retry on failure"""
    def decorator(func: F) -> F:
        @functools.wraps(func)
        async def wrapper(self, *args, **kwargs):
            last_exception = None

            for attempt in range(max_retries):
                try:
                    return await func(self, *args, **kwargs)
                except anyio.get_cancelled_exc_class():
                    _LOGGER.warning(f"{func.__name__} cancelled")
                    raise
                except Exception as e:
                    last_exception = e
                    if attempt < (max_retries - 1):
                        _LOGGER.warning(
                            f"{func.__name__} retry {attempt + 1}: {e}"
                        )
                        await anyio.sleep(delay)

            _LOGGER.error(f"{func.__name__} all retries failed")
            raise last_exception
        return wrapper
    return decorator


def with_hass_manager(func: F) -> F:
    """Decorator: Provide hass_manager checking and error handling for MCP tools"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # 從函數的全局命名空間獲取hass_manager
        hass_manager = func.__globals__.get('hass_manager')

        if not hass_manager:
            return {"error": "Home Assistant not connected"}

        try:
            # 將hass_manager作為第一個參數傳遞給函數
            return await func(hass_manager, *args, **kwargs)
        except Exception as e:
            _LOGGER.error(f"Error in {func.__name__}: {e}")
            return {"error": str(e)}
    return wrapper


def mcp_tool_error_handler(func: F) -> F:
    """Decorator: Unified error handling for MCP tool functions"""
    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            _LOGGER.error(f"Error in MCP tool {func.__name__}: {e}")
            return {"error": str(e)}
    return wrapper
